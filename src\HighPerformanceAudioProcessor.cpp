#include "HighPerformanceAudioProcessor.h"
#include <algorithm>
#include <cmath>
#include <chrono>
#include <iostream>

HighPerformanceAudioProcessor::HighPerformanceAudioProcessor()
    : running_(false), speechActive_(false), currentEnergy_(0.0f), noiseLevel_(0.001f),
      inSpeech_(false), speechFrames_(0), silenceFrames_(0),
      runningMean_(0.0f), runningVariance_(0.0f), frameCount_(0) {
}

HighPerformanceAudioProcessor::~HighPerformanceAudioProcessor() {
    Shutdown();
}

bool HighPerformanceAudioProcessor::Initialize(const Config& config) {
    config_ = config;
    
    // Initialize WebRTC VAD
    webrtcVad_ = std::make_unique<WebRTCVAD>();
    if (!webrtcVad_->Initialize(config.sampleRate, config.vadMode)) {
        std::cerr << "Failed to initialize WebRTC VAD" << std::endl;
        return false;
    }
    
    // Reset state
    speechActive_ = false;
    currentEnergy_ = 0.0f;
    noiseLevel_ = 0.001f;
    inSpeech_ = false;
    speechFrames_ = 0;
    silenceFrames_ = 0;
    runningMean_ = 0.0f;
    runningVariance_ = 0.0f;
    frameCount_ = 0;
    
    preRecordingBuffer_.clear();
    currentSpeechBuffer_.clear();
    
    std::cout << "HighPerformanceAudioProcessor initialized" << std::endl;
    return true;
}

void HighPerformanceAudioProcessor::SetCallback(AudioCallback callback) {
    callback_ = callback;
}

void HighPerformanceAudioProcessor::Start() {
    if (running_) return;
    
    running_ = true;
    processingThread_ = std::thread(&HighPerformanceAudioProcessor::ProcessingThread, this);
    std::cout << "HighPerformanceAudioProcessor started" << std::endl;
}

void HighPerformanceAudioProcessor::Stop() {
    if (!running_) return;
    
    running_ = false;
    bufferCondition_.notify_all();
    
    if (processingThread_.joinable()) {
        processingThread_.join();
    }
    
    std::cout << "HighPerformanceAudioProcessor stopped" << std::endl;
}

void HighPerformanceAudioProcessor::ProcessAudioFrame(const std::vector<float>& audioData) {
    if (!running_ || !webrtcVad_) return;
    
    // Preprocess audio
    auto processedAudio = audioData;
    if (config_.enableAudioNormalization) {
        processedAudio = NormalizeAudio(processedAudio);
    }
    if (config_.enableNoiseReduction) {
        processedAudio = ReduceNoise(processedAudio);
    }
    
    // Calculate energy for monitoring
    float energy = 0.0f;
    for (float sample : processedAudio) {
        energy += sample * sample;
    }
    energy = std::sqrt(energy / processedAudio.size());
    currentEnergy_ = energy;
    
    // Update noise level estimation
    if (frameCount_ < 100 || energy < noiseLevel_ * 2.0f) {
        noiseLevel_ = 0.95f * noiseLevel_ + 0.05f * energy;
    }
    frameCount_++;
    
    // WebRTC VAD detection
    bool vadDetection = webrtcVad_->ProcessFrame(processedAudio);
    
    // Add to pre-recording buffer
    {
        std::lock_guard<std::mutex> lock(bufferMutex_);
        preRecordingBuffer_.push_back(processedAudio);
        if (preRecordingBuffer_.size() > MAX_PRE_RECORDING_FRAMES) {
            preRecordingBuffer_.pop_front();
        }
    }
    
    // Speech state machine
    if (vadDetection) {
        speechFrames_++;
        silenceFrames_ = 0;
        
        if (!inSpeech_ && speechFrames_ >= MIN_SPEECH_FRAMES) {
            // Speech started
            inSpeech_ = true;
            speechActive_ = true;
            speechStartTime_ = std::chrono::steady_clock::now();
            
            // Add pre-recording buffer to current speech
            {
                std::lock_guard<std::mutex> lock(bufferMutex_);
                currentSpeechBuffer_.clear();
                for (const auto& frame : preRecordingBuffer_) {
                    currentSpeechBuffer_.insert(currentSpeechBuffer_.end(), frame.begin(), frame.end());
                }
            }
            
            std::cout << "Speech started (WebRTC VAD)" << std::endl;
        }
        
        if (inSpeech_) {
            lastSpeechTime_ = std::chrono::steady_clock::now();
            // Add current frame to speech buffer
            currentSpeechBuffer_.insert(currentSpeechBuffer_.end(), processedAudio.begin(), processedAudio.end());
        }
    } else {
        silenceFrames_++;
        speechFrames_ = std::max(0, speechFrames_ - 1);
        
        if (inSpeech_) {
            // Add silence frame to speech buffer (might be short pause)
            currentSpeechBuffer_.insert(currentSpeechBuffer_.end(), processedAudio.begin(), processedAudio.end());
            
            if (silenceFrames_ >= POST_SPEECH_SILENCE_FRAMES) {
                // Speech ended
                auto speechDuration = std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::steady_clock::now() - speechStartTime_).count() / 1000.0f;
                
                if (speechDuration >= config_.minRecordingDuration) {
                    // Valid speech segment - notify for processing
                    bufferCondition_.notify_one();
                    std::cout << "Speech ended, duration: " << speechDuration << "s" << std::endl;
                } else {
                    std::cout << "Speech too short: " << speechDuration << "s, discarding" << std::endl;
                    currentSpeechBuffer_.clear();
                }
                
                inSpeech_ = false;
                speechActive_ = false;
                silenceFrames_ = 0;
                speechFrames_ = 0;
            }
        }
    }
}

void HighPerformanceAudioProcessor::ProcessingThread() {
    std::cout << "=== ProcessingThread started ===" << std::endl;
    
    while (running_) {
        std::vector<float> speechDataToProcess;
        
        // Wait for speech data to be available
        {
            std::unique_lock<std::mutex> lock(bufferMutex_);
            
            // Simplified condition: just wait for speech data when not in active speech
            bufferCondition_.wait(lock, [this] { 
                bool hasData = !currentSpeechBuffer_.empty() && !inSpeech_;
                if (hasData) {
                    std::cout << "ProcessingThread: Speech data ready, size=" << currentSpeechBuffer_.size() 
                             << ", inSpeech=" << inSpeech_ << std::endl;
                }
                return !running_ || hasData;
            });
            
            if (!running_) {
                std::cout << "ProcessingThread: Stopping" << std::endl;
                break;
            }
            
            // Copy speech data and clear buffer atomically
            if (!currentSpeechBuffer_.empty()) {
                speechDataToProcess = std::move(currentSpeechBuffer_);
                currentSpeechBuffer_.clear();
                std::cout << "ProcessingThread: Captured speech data, size=" << speechDataToProcess.size() << std::endl;
            }
        }
        
        // Process outside the lock to avoid blocking audio processing
        if (!speechDataToProcess.empty() && callback_) {
            std::cout << "=== SENDING TO TRANSCRIPTION: " << speechDataToProcess.size() << " samples ===" << std::endl;
            try {
                callback_(speechDataToProcess, true);
                std::cout << "=== TRANSCRIPTION CALLBACK COMPLETED ===" << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "ProcessingThread: Callback error: " << e.what() << std::endl;
            }
        } else if (speechDataToProcess.empty()) {
            std::cout << "ProcessingThread: No speech data to process" << std::endl;
        } else if (!callback_) {
            std::cout << "ProcessingThread: No callback set!" << std::endl;
        }
    }
}

std::vector<float> HighPerformanceAudioProcessor::NormalizeAudio(const std::vector<float>& audio) {
    if (audio.empty()) return audio;
    
    // Find peak amplitude
    float maxAmp = 0.0f;
    for (float sample : audio) {
        maxAmp = std::max(maxAmp, std::abs(sample));
    }
    
    // Normalize if amplitude is too low or too high
    std::vector<float> normalized = audio;
    if (maxAmp > 0.001f && maxAmp < 0.1f) {
        float scale = 0.3f / maxAmp;
        for (float& sample : normalized) {
            sample *= scale;
        }
    } else if (maxAmp > 0.9f) {
        float scale = 0.8f / maxAmp;
        for (float& sample : normalized) {
            sample *= scale;
        }
    }
    
    return normalized;
}

std::vector<float> HighPerformanceAudioProcessor::ReduceNoise(const std::vector<float>& audio) {
    if (audio.empty()) return audio;
    
    std::vector<float> denoised = audio;
    
    // Simple noise gate - attenuate samples below noise threshold
    float noiseThreshold = noiseLevel_ * 1.5f;
    for (float& sample : denoised) {
        if (std::abs(sample) < noiseThreshold) {
            sample *= 0.1f; // Reduce noise by 90%
        }
    }
    
    return denoised;
}

void HighPerformanceAudioProcessor::Shutdown() {
    Stop();
    
    if (webrtcVad_) {
        webrtcVad_->Shutdown();
        webrtcVad_.reset();
    }
    
    preRecordingBuffer_.clear();
    currentSpeechBuffer_.clear();
}