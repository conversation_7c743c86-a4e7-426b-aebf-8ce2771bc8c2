#include <iostream>
#include <vector>
#include <chrono>
#include <thread>

// Simple unit test for transcription pipeline
int main() {
    std::cout << "=== UltraFlexSTT Transcription Pipeline Test ===" << std::endl;
    
    // Test 1: Audio data simulation
    std::cout << "Test 1: Simulating audio data..." << std::endl;
    std::vector<float> testAudio(16000, 0.0f); // 1 second of silence
    
    // Add some test audio pattern (sine wave)
    for (size_t i = 0; i < testAudio.size(); i++) {
        testAudio[i] = 0.1f * sin(2.0f * 3.14159f * 440.0f * i / 16000.0f); // 440Hz tone
    }
    
    std::cout << "Generated test audio: " << testAudio.size() << " samples" << std::endl;
    
    // Test 2: Callback chain simulation
    std::cout << "Test 2: Testing callback chain..." << std::endl;
    
    bool callbackCalled = false;
    auto testCallback = [&callbackCalled](const std::vector<float>& audio, bool isEndOfSpeech) {
        std::cout << "Callback triggered: " << audio.size() << " samples, endOfSpeech=" << isEndOfSpeech << std::endl;
        callbackCalled = true;
    };
    
    // Simulate callback
    testCallback(testAudio, true);
    
    if (callbackCalled) {
        std::cout << "✅ Callback test PASSED" << std::endl;
    } else {
        std::cout << "❌ Callback test FAILED" << std::endl;
    }
    
    // Test 3: Memory usage check
    std::cout << "Test 3: Memory usage check..." << std::endl;
    std::vector<float> largeBuffer(160000, 0.0f); // 10 seconds of audio
    std::cout << "Large buffer allocated: " << largeBuffer.size() * sizeof(float) / 1024 / 1024 << " MB" << std::endl;
    
    std::cout << "=== All tests completed ===" << std::endl;
    return 0;
}