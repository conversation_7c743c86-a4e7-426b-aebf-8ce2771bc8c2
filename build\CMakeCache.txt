# This is the CMakeCache file.
# For build in directory: c:/AI/UltraFlexSTT_CPP/build
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//ggml: build shared libraries
BUILD_SHARED_LIBS:BOOL=OFF

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=Boost_DIR-NOTFOUND

//Path to a file.
Boost_INCLUDE_DIR:PATH=Boost_INCLUDE_DIR-NOTFOUND

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/addr2line.exe

//Path to a program.
CMAKE_AR:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/ar.exe

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:STRING=Debug

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//No help, variable specified on the command line.
CMAKE_CXX_COMPILER:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/g++.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/gcc-ranlib.exe

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32

//No help, variable specified on the command line.
CMAKE_C_COMPILER:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/gcc.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/gcc-ranlib.exe

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/dlltool.exe

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/pkgRedirects

//Convert GNU import libraries to MS format (requires Visual Studio)
CMAKE_GNUtoMS:BOOL=OFF

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/UltraFlexSTT

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/ld.exe

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/nm.exe

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/objcopy.exe

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/objdump.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=UltraFlexSTT

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/ranlib.exe

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/windres.exe

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_READELF:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/readelf.exe

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/strip.exe

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Directory under which to collect all populated content
FETCHCONTENT_BASE_DIR:PATH=C:/AI/UltraFlexSTT_CPP/build/_deps

//Disables all attempts to download or update content and assumes
// source dirs already exist
FETCHCONTENT_FULLY_DISCONNECTED:BOOL=OFF

//Enables QUIET option for all content population
FETCHCONTENT_QUIET:BOOL=ON

//When not empty, overrides where to find pre-populated content
// for googletest
FETCHCONTENT_SOURCE_DIR_GOOGLETEST:PATH=

//Enables UPDATE_DISCONNECTED behavior for all content population
FETCHCONTENT_UPDATES_DISCONNECTED:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of googletest
FETCHCONTENT_UPDATES_DISCONNECTED_GOOGLETEST:BOOL=OFF

//ggml: enable Accelerate framework
GGML_ACCELERATE:BOOL=ON

//ggml: enable all compiler warnings in 3rd party libs
GGML_ALL_WARNINGS_3RD_PARTY:BOOL=OFF

//ggml: enable AMX-BF16
GGML_AMX_BF16:BOOL=OFF

//ggml: enable AMX-INT8
GGML_AMX_INT8:BOOL=OFF

//ggml: enable AMX-TILE
GGML_AMX_TILE:BOOL=OFF

//ggml: enable AVX
GGML_AVX:BOOL=OFF

//ggml: enable AVX2
GGML_AVX2:BOOL=OFF

//ggml: enable AVX512F
GGML_AVX512:BOOL=OFF

//ggml: enable AVX512-BF16
GGML_AVX512_BF16:BOOL=OFF

//ggml: enable AVX512-VBMI
GGML_AVX512_VBMI:BOOL=OFF

//ggml: enable AVX512-VNNI
GGML_AVX512_VNNI:BOOL=OFF

//ggml: enable AVX-VNNI
GGML_AVX_VNNI:BOOL=OFF

//ggml: build backends as dynamic libraries (requires BUILD_SHARED_LIBS)
GGML_BACKEND_DL:BOOL=OFF

//Location of binary  files
GGML_BIN_INSTALL_DIR:PATH=bin

//ggml: use BLAS
GGML_BLAS:BOOL=OFF

//ggml: BLAS library vendor
GGML_BLAS_VENDOR:STRING=Generic

//ggml: enable BMI2
GGML_BMI2:BOOL=OFF

//ggml: build examples
GGML_BUILD_EXAMPLES:BOOL=OFF

//ggml: build tests
GGML_BUILD_TESTS:BOOL=OFF

//ggml: use ccache if available
GGML_CCACHE:BOOL=ON

//Path to a program.
GGML_CCACHE_FOUND:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/ccache.exe

//ggml: enable CPU backend
GGML_CPU:BOOL=ON

//ggml: use runtime weight conversion of Q4_0 to Q4_X_X
GGML_CPU_AARCH64:BOOL=ON

//ggml: build all variants of the CPU backend (requires GGML_BACKEND_DL)
GGML_CPU_ALL_VARIANTS:BOOL=OFF

//ggml: CPU architecture for ARM
GGML_CPU_ARM_ARCH:STRING=

//ggml: use memkind for CPU HBM
GGML_CPU_HBM:BOOL=OFF

//ggml: use KleidiAI optimized kernels if applicable
GGML_CPU_KLEIDIAI:BOOL=OFF

//ggml: CPU type for PowerPC
GGML_CPU_POWERPC_CPUTYPE:STRING=

//ggml: use CUDA
GGML_CUDA:BOOL=OFF

//ggml: cuda link binary compression mode; requires cuda 12.8+
GGML_CUDA_COMPRESSION_MODE:STRING=size

//ggml: use 16 bit floats for some calculations
GGML_CUDA_F16:BOOL=OFF

//ggml: compile ggml FlashAttention CUDA kernels
GGML_CUDA_FA:BOOL=ON

//ggml: compile all quants for FlashAttention
GGML_CUDA_FA_ALL_QUANTS:BOOL=OFF

//ggml: always use cuBLAS instead of mmq kernels
GGML_CUDA_FORCE_CUBLAS:BOOL=OFF

//ggml: use mmq kernels instead of cuBLAS
GGML_CUDA_FORCE_MMQ:BOOL=OFF

//ggml: use CUDA graphs (llama.cpp only)
GGML_CUDA_GRAPHS:BOOL=OFF

//ggml: do not use peer to peer copies
GGML_CUDA_NO_PEER_COPY:BOOL=OFF

//ggml: do not try to use CUDA VMM
GGML_CUDA_NO_VMM:BOOL=OFF

//ggml: max. batch size for using peer access
GGML_CUDA_PEER_MAX_BATCH_SIZE:STRING=128

//ggml: enable F16C
GGML_F16C:BOOL=OFF

//ggml: enable FMA
GGML_FMA:BOOL=OFF

//ggml: enable gprof
GGML_GPROF:BOOL=OFF

//ggml: use HIP
GGML_HIP:BOOL=OFF

//ggml: use HIP graph, experimental, slow
GGML_HIP_GRAPHS:BOOL=OFF

//ggml: do not try to use HIP VMM
GGML_HIP_NO_VMM:BOOL=ON

//ggml: enable rocWMMA for FlashAttention
GGML_HIP_ROCWMMA_FATTN:BOOL=OFF

//Location of header  files
GGML_INCLUDE_INSTALL_DIR:PATH=include

//ggml: use Kompute
GGML_KOMPUTE:BOOL=OFF

//ggml: enable lasx
GGML_LASX:BOOL=ON

//Location of library files
GGML_LIB_INSTALL_DIR:PATH=lib

//ggml: use LLAMAFILE
GGML_LLAMAFILE:BOOL=OFF

//ggml: enable lsx
GGML_LSX:BOOL=ON

//ggml: enable link time optimization
GGML_LTO:BOOL=OFF

//ggml: use Metal
GGML_METAL:BOOL=OFF

//ggml: embed Metal library
GGML_METAL_EMBED_LIBRARY:BOOL=OFF

//ggml: metal minimum macOS version
GGML_METAL_MACOSX_VERSION_MIN:STRING=

//ggml: disable Metal debugging
GGML_METAL_NDEBUG:BOOL=OFF

//ggml: compile Metal with -fno-fast-math
GGML_METAL_SHADER_DEBUG:BOOL=OFF

//ggml: metal standard version (-std flag)
GGML_METAL_STD:STRING=

//ggml: use bfloat if available
GGML_METAL_USE_BF16:BOOL=OFF

//ggml: use MUSA
GGML_MUSA:BOOL=OFF

//ggml: optimize the build for the current system
GGML_NATIVE:BOOL=ON

//ggml: use OpenCL
GGML_OPENCL:BOOL=OFF

//ggml: embed kernels
GGML_OPENCL_EMBED_KERNELS:BOOL=ON

//ggml: use OpenCL profiling (increases overhead)
GGML_OPENCL_PROFILING:BOOL=OFF

//gmml: OpenCL API version to target
GGML_OPENCL_TARGET_VERSION:STRING=300

//ggml: use optimized kernels for Adreno
GGML_OPENCL_USE_ADRENO_KERNELS:BOOL=ON

//ggml: use OpenMP
GGML_OPENMP:BOOL=ON

//ggml: use RPC
GGML_RPC:BOOL=OFF

//ggml: enable rvv
GGML_RVV:BOOL=ON

//ggml: enable riscv zfh
GGML_RV_ZFH:BOOL=OFF

//Path to a program.
GGML_SCCACHE_FOUND:FILEPATH=GGML_SCCACHE_FOUND-NOTFOUND

//ggml: max input copies for pipeline parallelism
GGML_SCHED_MAX_COPIES:STRING=4

//ggml: enable SSE 4.2
GGML_SSE42:BOOL=OFF

//ggml: static link libraries
GGML_STATIC:BOOL=OFF

//ggml: use SYCL
GGML_SYCL:BOOL=OFF

//ggml: sycl device architecture
GGML_SYCL_DEVICE_ARCH:STRING=

//ggml: enable oneDNN in the SYCL backend
GGML_SYCL_DNN:BOOL=ON

//ggml: use 16 bit floats for sycl calculations
GGML_SYCL_F16:BOOL=OFF

//ggml: enable graphs in the SYCL backend
GGML_SYCL_GRAPH:BOOL=ON

//ggml: sycl target device
GGML_SYCL_TARGET:STRING=INTEL

//ggml: use Vulkan
GGML_VULKAN:BOOL=OFF

//ggml: run Vulkan op checks
GGML_VULKAN_CHECK_RESULTS:BOOL=OFF

//ggml: enable Vulkan debug output
GGML_VULKAN_DEBUG:BOOL=OFF

//ggml: enable Vulkan memory debug output
GGML_VULKAN_MEMORY_DEBUG:BOOL=OFF

//ggml: enable Vulkan perf output
GGML_VULKAN_PERF:BOOL=OFF

//ggml: run Vulkan tests
GGML_VULKAN_RUN_TESTS:BOOL=OFF

//ggml: toolchain file for vulkan-shaders-gen
GGML_VULKAN_SHADERS_GEN_TOOLCHAIN:FILEPATH=

//ggml: enable Vulkan shader debug info
GGML_VULKAN_SHADER_DEBUG_INFO:BOOL=OFF

//ggml: enable Vulkan validation
GGML_VULKAN_VALIDATE:BOOL=OFF

//ggml: enable vxe
GGML_VXE:BOOL=ON

//ggml: Windows version
GGML_WIN_VER:STRING=0x602

//Path to a program.
GIT_EXE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//Git command line client
GIT_EXECUTABLE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//JACK header
JACK_INCLUDEDIR:PATH=JACK_INCLUDEDIR-NOTFOUND

//JACK library
JACK_LINK_LIBRARIES:FILEPATH=JACK_LINK_LIBRARIES-NOTFOUND

//Path to a library.
MATH_LIBRARY:FILEPATH=MATH_LIBRARY-NOTFOUND

//CXX compiler flags for OpenMP parallelization
OpenMP_CXX_FLAGS:STRING=-fopenmp

//CXX compiler libraries for OpenMP parallelization
OpenMP_CXX_LIB_NAMES:STRING=gomp;mingwthrd

//C compiler flags for OpenMP parallelization
OpenMP_C_FLAGS:STRING=-fopenmp

//C compiler libraries for OpenMP parallelization
OpenMP_C_LIB_NAMES:STRING=gomp;mingwthrd

//Path to the gomp library for OpenMP
OpenMP_gomp_LIBRARY:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/libgomp.dll.a

//Path to the mingwthrd library for OpenMP
OpenMP_mingwthrd_LIBRARY:FILEPATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib/libmingwthrd.a

//Include example projects
PA_BUILD_EXAMPLES:BOOL=OFF

PA_BUILD_SHARED:BOOL=OFF

//Build dynamic library
PA_BUILD_SHARED_LIBS:BOOL=OFF

PA_BUILD_STATIC:BOOL=ON

//Include test projects
PA_BUILD_TESTS:BOOL=OFF

//Enable debug output for Portaudio
PA_ENABLE_DEBUG_OUTPUT:BOOL=OFF

//Enable support for ASIO
PA_USE_ASIO:BOOL=OFF

//Enable support for DirectSound
PA_USE_DS:BOOL=ON

//Use skeleton host API
PA_USE_SKELETON:BOOL=OFF

//Enable support for WASAPI
PA_USE_WASAPI:BOOL=ON

//Enable support for WDMKS
PA_USE_WDMKS:BOOL=ON

//Use WDM/KS API for device info
PA_USE_WDMKS_DEVICE_INFO:BOOL=ON

//Enable support for WMME
PA_USE_WMME:BOOL=ON

//Turn compiler warnings into errors
PA_WARNINGS_ARE_ERRORS:BOOL=OFF

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=PKG_CONFIG_EXECUTABLE-NOTFOUND

//Value Computed by CMake
PortAudio_BINARY_DIR:STATIC=C:/AI/UltraFlexSTT_CPP/build/external/portaudio

//Value Computed by CMake
PortAudio_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
PortAudio_SOURCE_DIR:STATIC=C:/AI/UltraFlexSTT_CPP/external/portaudio

//Path to a library.
TRE_LIBRARY:FILEPATH=TRE_LIBRARY-NOTFOUND

//Path to a file.
TRE_REGEX_H:PATH=TRE_REGEX_H-NOTFOUND

//Value Computed by CMake
UltraFlexSTT_BINARY_DIR:STATIC=C:/AI/UltraFlexSTT_CPP/build

//Value Computed by CMake
UltraFlexSTT_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
UltraFlexSTT_SOURCE_DIR:STATIC=C:/AI/UltraFlexSTT_CPP

//Value Computed by CMake
UltraFlexSTT_Tests_BINARY_DIR:STATIC=C:/AI/UltraFlexSTT_CPP/build/tests

//Value Computed by CMake
UltraFlexSTT_Tests_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
UltraFlexSTT_Tests_SOURCE_DIR:STATIC=C:/AI/UltraFlexSTT_CPP/tests

//whisper: enable all compiler warnings
WHISPER_ALL_WARNINGS:BOOL=ON

//whisper: enable all compiler warnings in 3rd party libs
WHISPER_ALL_WARNINGS_3RD_PARTY:BOOL=OFF

//Location of binary  files
WHISPER_BIN_INSTALL_DIR:PATH=bin

//whisper: build examples
WHISPER_BUILD_EXAMPLES:BOOL=OFF

//whisper: build server example
WHISPER_BUILD_SERVER:BOOL=OFF

//whisper: build tests
WHISPER_BUILD_TESTS:BOOL=OFF

//whisper: enable Core ML framework
WHISPER_COREML:BOOL=OFF

//whisper: allow non-CoreML fallback
WHISPER_COREML_ALLOW_FALLBACK:BOOL=OFF

//whisper: use libcurl to download model from an URL
WHISPER_CURL:BOOL=OFF

//whisper: enable -Werror flag
WHISPER_FATAL_WARNINGS:BOOL=OFF

//Location of header  files
WHISPER_INCLUDE_INSTALL_DIR:PATH=include

//Location of library files
WHISPER_LIB_INSTALL_DIR:PATH=lib

//whisper: support for OpenVINO
WHISPER_OPENVINO:BOOL=OFF

//whisper: enable address sanitizer
WHISPER_SANITIZE_ADDRESS:BOOL=OFF

//whisper: enable thread sanitizer
WHISPER_SANITIZE_THREAD:BOOL=OFF

//whisper: enable undefined sanitizer
WHISPER_SANITIZE_UNDEFINED:BOOL=OFF

//whisper: support for libSDL2
WHISPER_SDL2:BOOL=OFF

//whisper: use system-installed GGML library
WHISPER_USE_SYSTEM_GGML:BOOL=OFF

//Value Computed by CMake
ggml_BINARY_DIR:STATIC=C:/AI/UltraFlexSTT_CPP/build/external/whisper.cpp/ggml

//Value Computed by CMake
ggml_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
ggml_SOURCE_DIR:STATIC=C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/ggml

//Dependencies for the target
portaudio_LIB_DEPENDS:STATIC=general;winmm;general;dsound;general;ole32;general;uuid;general;ole32;general;uuid;general;setupapi;general;ole32;general;uuid;

//Value Computed by CMake
whisper.cpp_BINARY_DIR:STATIC=C:/AI/UltraFlexSTT_CPP/build/external/whisper.cpp

//Value Computed by CMake
whisper.cpp_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
whisper.cpp_SOURCE_DIR:STATIC=C:/AI/UltraFlexSTT_CPP/external/whisper.cpp

//Dependencies for the target
whisper_LIB_DEPENDS:STATIC=general;ggml;


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_INCLUDE_DIR
Boost_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/AI/UltraFlexSTT_CPP/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake-gui.exe
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=MinGW Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/AI/UltraFlexSTT_CPP
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=8
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.31
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Git
FIND_PACKAGE_MESSAGE_DETAILS_Git:INTERNAL=[C:/Program Files/Git/cmd/git.exe][v2.47.1.windows.1()]
//Details about finding OpenMP
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP:INTERNAL=[TRUE][TRUE][c ][v4.5()]
//Details about finding OpenMP_C
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_C:INTERNAL=[-fopenmp][C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/libgomp.dll.a][C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib/libmingwthrd.a][v4.5()]
//Details about finding OpenMP_CXX
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_CXX:INTERNAL=[-fopenmp][C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/libgomp.dll.a][C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib/libmingwthrd.a][v4.5()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//List of backends for cmake package
GGML_AVAILABLE_BACKENDS:INTERNAL=ggml-cpu
//Test GGML_COMPILER_SUPPORTS_FP16_FORMAT_I3E
GGML_COMPILER_SUPPORTS_FP16_FORMAT_I3E:INTERNAL=
//STRINGS property for variable: GGML_CUDA_COMPRESSION_MODE
GGML_CUDA_COMPRESSION_MODE-STRINGS:INTERNAL=none;speed;balance;size
//Test GGML_MACHINE_SUPPORTS_dotprod
GGML_MACHINE_SUPPORTS_dotprod:INTERNAL=
//Result of TRY_COMPILE
GGML_MACHINE_SUPPORTS_dotprod_COMPILED:INTERNAL=FALSE
//Test GGML_MACHINE_SUPPORTS_i8mm
GGML_MACHINE_SUPPORTS_i8mm:INTERNAL=
//Result of TRY_COMPILE
GGML_MACHINE_SUPPORTS_i8mm_COMPILED:INTERNAL=FALSE
//Test GGML_MACHINE_SUPPORTS_nodotprod
GGML_MACHINE_SUPPORTS_nodotprod:INTERNAL=
//Test GGML_MACHINE_SUPPORTS_noi8mm
GGML_MACHINE_SUPPORTS_noi8mm:INTERNAL=
//Test GGML_MACHINE_SUPPORTS_nosme
GGML_MACHINE_SUPPORTS_nosme:INTERNAL=
//Test GGML_MACHINE_SUPPORTS_nosve
GGML_MACHINE_SUPPORTS_nosve:INTERNAL=
//Test GGML_MACHINE_SUPPORTS_sme
GGML_MACHINE_SUPPORTS_sme:INTERNAL=
//Result of TRY_COMPILE
GGML_MACHINE_SUPPORTS_sme_COMPILED:INTERNAL=FALSE
//Test GGML_MACHINE_SUPPORTS_sve
GGML_MACHINE_SUPPORTS_sve:INTERNAL=
//Result of TRY_COMPILE
GGML_MACHINE_SUPPORTS_sve_COMPILED:INTERNAL=FALSE
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_CXX_fopenmp:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_C_fopenmp:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_CXX_FLAGS
OpenMP_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_CXX_LIB_NAMES
OpenMP_CXX_LIB_NAMES-ADVANCED:INTERNAL=1
//CXX compiler's OpenMP specification date
OpenMP_CXX_SPEC_DATE:INTERNAL=201511
//ADVANCED property for variable: OpenMP_C_FLAGS
OpenMP_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_LIB_NAMES
OpenMP_C_LIB_NAMES-ADVANCED:INTERNAL=1
//C compiler's OpenMP specification date
OpenMP_C_SPEC_DATE:INTERNAL=201511
//Result of TRY_COMPILE
OpenMP_SPECTEST_CXX_:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_SPECTEST_C_:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_gomp_LIBRARY
OpenMP_gomp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_mingwthrd_LIBRARY
OpenMP_mingwthrd_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//Have include regex.h
REGEX_H:INTERNAL=
//Components requested for this build tree.
_Boost_COMPONENTS_SEARCHED:INTERNAL=
//Last used Boost_INCLUDE_DIR value.
_Boost_INCLUDE_DIR_LAST:INTERNAL=Boost_INCLUDE_DIR-NOTFOUND
//Last used Boost_NAMESPACE value.
_Boost_NAMESPACE_LAST:INTERNAL=boost
//Last used Boost_USE_MULTITHREADED value.
_Boost_USE_MULTITHREADED_LAST:INTERNAL=TRUE
//linker supports push/pop state
_CMAKE_CXX_LINKER_PUSHPOP_STATE_SUPPORTED:INTERNAL=FALSE
//linker supports push/pop state
_CMAKE_C_LINKER_PUSHPOP_STATE_SUPPORTED:INTERNAL=FALSE
//linker supports push/pop state
_CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED:INTERNAL=TRUE
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=C:/Program Files (x86)/UltraFlexSTT

