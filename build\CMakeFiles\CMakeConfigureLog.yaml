
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - ARM64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
      Build started 5/27/2025 3:52:09 PM.
      
      Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostarm64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.65
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/3.31.0-rc2/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
      Build started 5/27/2025 3:52:10 PM.
      
      Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostarm64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.63
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/3.31.0-rc2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-l6g0z1"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-l6g0z1"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-l6g0z1'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_d9a39.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:11 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l6g0z1\\cmTC_d9a39.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_d9a39.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l6g0z1\\Debug\\".
          Creating directory "cmTC_d9a39.dir\\Debug\\cmTC_d9a39.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_d9a39.dir\\Debug\\cmTC_d9a39.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_d9a39.dir\\Debug\\cmTC_d9a39.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_d9a39.dir\\Debug\\\\" /Fd"cmTC_d9a39.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_d9a39.dir\\Debug\\\\" /Fd"cmTC_d9a39.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l6g0z1\\Debug\\cmTC_d9a39.exe" /INCREMENTAL /ILK:"cmTC_d9a39.dir\\Debug\\cmTC_d9a39.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-l6g0z1/Debug/cmTC_d9a39.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-l6g0z1/Debug/cmTC_d9a39.lib" /MACHINE:X64  /machine:x64 cmTC_d9a39.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_d9a39.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l6g0z1\\Debug\\cmTC_d9a39.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_d9a39.dir\\Debug\\cmTC_d9a39.tlog\\unsuccessfulbuild".
          Touching "cmTC_d9a39.dir\\Debug\\cmTC_d9a39.tlog\\cmTC_d9a39.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l6g0z1\\cmTC_d9a39.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.58
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-odhqrr"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-odhqrr"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-odhqrr'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_145c2.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:12 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-odhqrr\\cmTC_145c2.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_145c2.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-odhqrr\\Debug\\".
          Creating directory "cmTC_145c2.dir\\Debug\\cmTC_145c2.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_145c2.dir\\Debug\\cmTC_145c2.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_145c2.dir\\Debug\\cmTC_145c2.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_145c2.dir\\Debug\\\\" /Fd"cmTC_145c2.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_145c2.dir\\Debug\\\\" /Fd"cmTC_145c2.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-odhqrr\\Debug\\cmTC_145c2.exe" /INCREMENTAL /ILK:"cmTC_145c2.dir\\Debug\\cmTC_145c2.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-odhqrr/Debug/cmTC_145c2.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-odhqrr/Debug/cmTC_145c2.lib" /MACHINE:X64  /machine:x64 cmTC_145c2.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_145c2.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-odhqrr\\Debug\\cmTC_145c2.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_145c2.dir\\Debug\\cmTC_145c2.tlog\\unsuccessfulbuild".
          Touching "cmTC_145c2.dir\\Debug\\cmTC_145c2.tlog\\cmTC_145c2.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-odhqrr\\cmTC_145c2.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.71
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:16 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0ja7hj"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0ja7hj"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0ja7hj'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_3e15c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:13 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0ja7hj\\cmTC_3e15c.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_3e15c.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0ja7hj\\Debug\\".
          Creating directory "cmTC_3e15c.dir\\Debug\\cmTC_3e15c.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_3e15c.dir\\Debug\\cmTC_3e15c.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_3e15c.dir\\Debug\\cmTC_3e15c.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3e15c.dir\\Debug\\\\" /Fd"cmTC_3e15c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0ja7hj\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3e15c.dir\\Debug\\\\" /Fd"cmTC_3e15c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0ja7hj\\src.c"
          src.c
        C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0ja7hj\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0ja7hj\\cmTC_3e15c.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0ja7hj\\cmTC_3e15c.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0ja7hj\\cmTC_3e15c.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0ja7hj\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0ja7hj\\cmTC_3e15c.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.34
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "CMakeLists.txt:16 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4t6sdm"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4t6sdm"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4t6sdm'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_76250.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:13 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4t6sdm\\cmTC_76250.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_76250.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4t6sdm\\Debug\\".
          Creating directory "cmTC_76250.dir\\Debug\\cmTC_76250.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_76250.dir\\Debug\\cmTC_76250.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_76250.dir\\Debug\\cmTC_76250.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_76250.dir\\Debug\\\\" /Fd"cmTC_76250.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4t6sdm\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_76250.dir\\Debug\\\\" /Fd"cmTC_76250.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4t6sdm\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4t6sdm\\Debug\\cmTC_76250.exe" /INCREMENTAL /ILK:"cmTC_76250.dir\\Debug\\cmTC_76250.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4t6sdm/Debug/cmTC_76250.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4t6sdm/Debug/cmTC_76250.lib" /MACHINE:X64  /machine:x64 cmTC_76250.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4t6sdm\\cmTC_76250.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4t6sdm\\cmTC_76250.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4t6sdm\\cmTC_76250.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4t6sdm\\cmTC_76250.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.51
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "CMakeLists.txt:16 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-m9yqy6"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-m9yqy6"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-m9yqy6'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_e92d9.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:14 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9yqy6\\cmTC_e92d9.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e92d9.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9yqy6\\Debug\\".
          Creating directory "cmTC_e92d9.dir\\Debug\\cmTC_e92d9.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e92d9.dir\\Debug\\cmTC_e92d9.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e92d9.dir\\Debug\\cmTC_e92d9.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e92d9.dir\\Debug\\\\" /Fd"cmTC_e92d9.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9yqy6\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e92d9.dir\\Debug\\\\" /Fd"cmTC_e92d9.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9yqy6\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9yqy6\\Debug\\cmTC_e92d9.exe" /INCREMENTAL /ILK:"cmTC_e92d9.dir\\Debug\\cmTC_e92d9.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-m9yqy6/Debug/cmTC_e92d9.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-m9yqy6/Debug/cmTC_e92d9.lib" /MACHINE:X64  /machine:x64 cmTC_e92d9.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9yqy6\\cmTC_e92d9.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9yqy6\\cmTC_e92d9.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9yqy6\\cmTC_e92d9.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9yqy6\\cmTC_e92d9.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.48
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-j0orhb"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-j0orhb"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-j0orhb'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_25498.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:15 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j0orhb\\cmTC_25498.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_25498.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j0orhb\\Debug\\".
          Creating directory "cmTC_25498.dir\\Debug\\cmTC_25498.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_25498.dir\\Debug\\cmTC_25498.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_25498.dir\\Debug\\cmTC_25498.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_25498.dir\\Debug\\\\" /Fd"cmTC_25498.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j0orhb\\OpenMPTryFlag.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_25498.dir\\Debug\\\\" /Fd"cmTC_25498.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j0orhb\\OpenMPTryFlag.c"
          OpenMPTryFlag.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j0orhb\\Debug\\cmTC_25498.exe" /INCREMENTAL /ILK:"cmTC_25498.dir\\Debug\\cmTC_25498.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-j0orhb/Debug/cmTC_25498.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-j0orhb/Debug/cmTC_25498.lib" /MACHINE:X64  /machine:x64 cmTC_25498.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_25498.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j0orhb\\Debug\\cmTC_25498.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_25498.dir\\Debug\\cmTC_25498.tlog\\unsuccessfulbuild".
          Touching "cmTC_25498.dir\\Debug\\cmTC_25498.tlog\\cmTC_25498.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j0orhb\\cmTC_25498.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-9zz84z"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-9zz84z"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-9zz84z'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_8239d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:16 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9zz84z\\cmTC_8239d.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_8239d.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9zz84z\\Debug\\".
          Creating directory "cmTC_8239d.dir\\Debug\\cmTC_8239d.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_8239d.dir\\Debug\\cmTC_8239d.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_8239d.dir\\Debug\\cmTC_8239d.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_8239d.dir\\Debug\\\\" /Fd"cmTC_8239d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9zz84z\\OpenMPTryFlag.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_8239d.dir\\Debug\\\\" /Fd"cmTC_8239d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9zz84z\\OpenMPTryFlag.cpp"
          OpenMPTryFlag.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9zz84z\\Debug\\cmTC_8239d.exe" /INCREMENTAL /ILK:"cmTC_8239d.dir\\Debug\\cmTC_8239d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-9zz84z/Debug/cmTC_8239d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-9zz84z/Debug/cmTC_8239d.lib" /MACHINE:X64  /machine:x64 cmTC_8239d.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_8239d.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9zz84z\\Debug\\cmTC_8239d.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_8239d.dir\\Debug\\cmTC_8239d.tlog\\unsuccessfulbuild".
          Touching "cmTC_8239d.dir\\Debug\\cmTC_8239d.tlog\\cmTC_8239d.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9zz84z\\cmTC_8239d.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:456 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:596 (_OPENMP_GET_SPEC_DATE)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP version"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-s6dius"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-s6dius"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_C_"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-s6dius'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_76f35.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:17 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s6dius\\cmTC_76f35.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_76f35.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s6dius\\Debug\\".
          Creating directory "cmTC_76f35.dir\\Debug\\cmTC_76f35.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_76f35.dir\\Debug\\cmTC_76f35.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_76f35.dir\\Debug\\cmTC_76f35.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_76f35.dir\\Debug\\\\" /Fd"cmTC_76f35.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s6dius\\OpenMPCheckVersion.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_76f35.dir\\Debug\\\\" /Fd"cmTC_76f35.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s6dius\\OpenMPCheckVersion.c"
          OpenMPCheckVersion.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s6dius\\Debug\\cmTC_76f35.exe" /INCREMENTAL /ILK:"cmTC_76f35.dir\\Debug\\cmTC_76f35.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-s6dius/Debug/cmTC_76f35.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-s6dius/Debug/cmTC_76f35.lib" /MACHINE:X64  /machine:x64 cmTC_76f35.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_76f35.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s6dius\\Debug\\cmTC_76f35.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_76f35.dir\\Debug\\cmTC_76f35.tlog\\unsuccessfulbuild".
          Touching "cmTC_76f35.dir\\Debug\\cmTC_76f35.tlog\\cmTC_76f35.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s6dius\\cmTC_76f35.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.53
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:456 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:596 (_OPENMP_GET_SPEC_DATE)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP version"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i1c5qi"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i1c5qi"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_CXX_"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i1c5qi'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_f20ce.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:18 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i1c5qi\\cmTC_f20ce.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_f20ce.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i1c5qi\\Debug\\".
          Creating directory "cmTC_f20ce.dir\\Debug\\cmTC_f20ce.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_f20ce.dir\\Debug\\cmTC_f20ce.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_f20ce.dir\\Debug\\cmTC_f20ce.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_f20ce.dir\\Debug\\\\" /Fd"cmTC_f20ce.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i1c5qi\\OpenMPCheckVersion.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_f20ce.dir\\Debug\\\\" /Fd"cmTC_f20ce.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i1c5qi\\OpenMPCheckVersion.cpp"
          OpenMPCheckVersion.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i1c5qi\\Debug\\cmTC_f20ce.exe" /INCREMENTAL /ILK:"cmTC_f20ce.dir\\Debug\\cmTC_f20ce.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i1c5qi/Debug/cmTC_f20ce.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i1c5qi/Debug/cmTC_f20ce.lib" /MACHINE:X64  /machine:x64 cmTC_f20ce.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_f20ce.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i1c5qi\\Debug\\cmTC_f20ce.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_f20ce.dir\\Debug\\cmTC_f20ce.tlog\\unsuccessfulbuild".
          Touching "cmTC_f20ce.dir\\Debug\\cmTC_f20ce.tlog\\cmTC_f20ce.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i1c5qi\\cmTC_f20ce.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:80 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-01w9bu"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-01w9bu"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-01w9bu'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_049af.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:19 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-01w9bu\\cmTC_049af.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_049af.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-01w9bu\\Debug\\".
          Creating directory "cmTC_049af.dir\\Debug\\cmTC_049af.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_049af.dir\\Debug\\cmTC_049af.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_049af.dir\\Debug\\cmTC_049af.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_049af.dir\\Debug\\\\" /Fd"cmTC_049af.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-01w9bu\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_049af.dir\\Debug\\\\" /Fd"cmTC_049af.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-01w9bu\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-01w9bu\\Debug\\cmTC_049af.exe" /INCREMENTAL /ILK:"cmTC_049af.dir\\Debug\\cmTC_049af.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-01w9bu/Debug/cmTC_049af.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-01w9bu/Debug/cmTC_049af.lib" /MACHINE:X64  /machine:x64 cmTC_049af.dir\\Debug\\src.obj
          cmTC_049af.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-01w9bu\\Debug\\cmTC_049af.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_049af.dir\\Debug\\cmTC_049af.tlog\\unsuccessfulbuild".
          Touching "cmTC_049af.dir\\Debug\\cmTC_049af.tlog\\cmTC_049af.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-01w9bu\\cmTC_049af.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.71
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:80 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-ig1305"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-ig1305"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-ig1305'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_890b5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:20 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ig1305\\cmTC_890b5.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_890b5.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ig1305\\Debug\\".
          Creating directory "cmTC_890b5.dir\\Debug\\cmTC_890b5.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_890b5.dir\\Debug\\cmTC_890b5.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_890b5.dir\\Debug\\cmTC_890b5.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_890b5.dir\\Debug\\\\" /Fd"cmTC_890b5.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ig1305\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_890b5.dir\\Debug\\\\" /Fd"cmTC_890b5.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ig1305\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ig1305\\Debug\\cmTC_890b5.exe" /INCREMENTAL /ILK:"cmTC_890b5.dir\\Debug\\cmTC_890b5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-ig1305/Debug/cmTC_890b5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-ig1305/Debug/cmTC_890b5.lib" /MACHINE:X64  /machine:x64 cmTC_890b5.dir\\Debug\\src.obj
          cmTC_890b5.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ig1305\\Debug\\cmTC_890b5.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_890b5.dir\\Debug\\cmTC_890b5.tlog\\unsuccessfulbuild".
          Touching "cmTC_890b5.dir\\Debug\\cmTC_890b5.tlog\\cmTC_890b5.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ig1305\\cmTC_890b5.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.58
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:87 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX2_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-me4f4c"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-me4f4c"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX2_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-me4f4c'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_67883.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:21 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-me4f4c\\cmTC_67883.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_67883.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-me4f4c\\Debug\\".
          Creating directory "cmTC_67883.dir\\Debug\\cmTC_67883.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_67883.dir\\Debug\\cmTC_67883.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_67883.dir\\Debug\\cmTC_67883.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_67883.dir\\Debug\\\\" /Fd"cmTC_67883.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-me4f4c\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_67883.dir\\Debug\\\\" /Fd"cmTC_67883.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-me4f4c\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-me4f4c\\Debug\\cmTC_67883.exe" /INCREMENTAL /ILK:"cmTC_67883.dir\\Debug\\cmTC_67883.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-me4f4c/Debug/cmTC_67883.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-me4f4c/Debug/cmTC_67883.lib" /MACHINE:X64  /machine:x64 cmTC_67883.dir\\Debug\\src.obj
          cmTC_67883.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-me4f4c\\Debug\\cmTC_67883.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_67883.dir\\Debug\\cmTC_67883.tlog\\unsuccessfulbuild".
          Touching "cmTC_67883.dir\\Debug\\cmTC_67883.tlog\\cmTC_67883.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-me4f4c\\cmTC_67883.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.76
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX2_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:87 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX2_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vgwapm"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vgwapm"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX2_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vgwapm'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_ef8be.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:22 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vgwapm\\cmTC_ef8be.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_ef8be.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vgwapm\\Debug\\".
          Creating directory "cmTC_ef8be.dir\\Debug\\cmTC_ef8be.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_ef8be.dir\\Debug\\cmTC_ef8be.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_ef8be.dir\\Debug\\cmTC_ef8be.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_ef8be.dir\\Debug\\\\" /Fd"cmTC_ef8be.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vgwapm\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_ef8be.dir\\Debug\\\\" /Fd"cmTC_ef8be.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vgwapm\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vgwapm\\Debug\\cmTC_ef8be.exe" /INCREMENTAL /ILK:"cmTC_ef8be.dir\\Debug\\cmTC_ef8be.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vgwapm/Debug/cmTC_ef8be.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vgwapm/Debug/cmTC_ef8be.lib" /MACHINE:X64  /machine:x64 cmTC_ef8be.dir\\Debug\\src.obj
          cmTC_ef8be.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vgwapm\\Debug\\cmTC_ef8be.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_ef8be.dir\\Debug\\cmTC_ef8be.tlog\\unsuccessfulbuild".
          Touching "cmTC_ef8be.dir\\Debug\\cmTC_ef8be.tlog\\cmTC_ef8be.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vgwapm\\cmTC_ef8be.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.77
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX2_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:88 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_FMA_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-qoijet"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-qoijet"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_FMA_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-qoijet'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_b6e96.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:23 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qoijet\\cmTC_b6e96.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_b6e96.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qoijet\\Debug\\".
          Creating directory "cmTC_b6e96.dir\\Debug\\cmTC_b6e96.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_b6e96.dir\\Debug\\cmTC_b6e96.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_b6e96.dir\\Debug\\cmTC_b6e96.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_b6e96.dir\\Debug\\\\" /Fd"cmTC_b6e96.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qoijet\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_b6e96.dir\\Debug\\\\" /Fd"cmTC_b6e96.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qoijet\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qoijet\\Debug\\cmTC_b6e96.exe" /INCREMENTAL /ILK:"cmTC_b6e96.dir\\Debug\\cmTC_b6e96.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-qoijet/Debug/cmTC_b6e96.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-qoijet/Debug/cmTC_b6e96.lib" /MACHINE:X64  /machine:x64 cmTC_b6e96.dir\\Debug\\src.obj
          cmTC_b6e96.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qoijet\\Debug\\cmTC_b6e96.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_b6e96.dir\\Debug\\cmTC_b6e96.tlog\\unsuccessfulbuild".
          Touching "cmTC_b6e96.dir\\Debug\\cmTC_b6e96.tlog\\cmTC_b6e96.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qoijet\\cmTC_b6e96.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.00
        
      exitCode: 0
    runResult:
      variable: "HAS_FMA_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:88 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_FMA_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-24e6k2"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-24e6k2"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_FMA_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-24e6k2'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_c15fd.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:25 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24e6k2\\cmTC_c15fd.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_c15fd.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24e6k2\\Debug\\".
          Creating directory "cmTC_c15fd.dir\\Debug\\cmTC_c15fd.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_c15fd.dir\\Debug\\cmTC_c15fd.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_c15fd.dir\\Debug\\cmTC_c15fd.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_c15fd.dir\\Debug\\\\" /Fd"cmTC_c15fd.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24e6k2\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_c15fd.dir\\Debug\\\\" /Fd"cmTC_c15fd.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24e6k2\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24e6k2\\Debug\\cmTC_c15fd.exe" /INCREMENTAL /ILK:"cmTC_c15fd.dir\\Debug\\cmTC_c15fd.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-24e6k2/Debug/cmTC_c15fd.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-24e6k2/Debug/cmTC_c15fd.lib" /MACHINE:X64  /machine:x64 cmTC_c15fd.dir\\Debug\\src.obj
          cmTC_c15fd.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24e6k2\\Debug\\cmTC_c15fd.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_c15fd.dir\\Debug\\cmTC_c15fd.tlog\\unsuccessfulbuild".
          Touching "cmTC_c15fd.dir\\Debug\\cmTC_c15fd.tlog\\cmTC_c15fd.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-24e6k2\\cmTC_c15fd.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.01
        
      exitCode: 0
    runResult:
      variable: "HAS_FMA_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:95 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX512_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4599ld"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4599ld"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX512_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4599ld'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_e0996.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:27 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4599ld\\cmTC_e0996.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e0996.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4599ld\\Debug\\".
          Creating directory "cmTC_e0996.dir\\Debug\\cmTC_e0996.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e0996.dir\\Debug\\cmTC_e0996.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e0996.dir\\Debug\\cmTC_e0996.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_e0996.dir\\Debug\\\\" /Fd"cmTC_e0996.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4599ld\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_e0996.dir\\Debug\\\\" /Fd"cmTC_e0996.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4599ld\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4599ld\\Debug\\cmTC_e0996.exe" /INCREMENTAL /ILK:"cmTC_e0996.dir\\Debug\\cmTC_e0996.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4599ld/Debug/cmTC_e0996.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4599ld/Debug/cmTC_e0996.lib" /MACHINE:X64  /machine:x64 cmTC_e0996.dir\\Debug\\src.obj
          cmTC_e0996.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4599ld\\Debug\\cmTC_e0996.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_e0996.dir\\Debug\\cmTC_e0996.tlog\\unsuccessfulbuild".
          Touching "cmTC_e0996.dir\\Debug\\cmTC_e0996.tlog\\cmTC_e0996.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4599ld\\cmTC_e0996.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.03
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX512_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:95 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX512_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4mz0dg"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4mz0dg"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX512_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4mz0dg'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_32c19.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:28 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4mz0dg\\cmTC_32c19.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_32c19.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4mz0dg\\Debug\\".
          Creating directory "cmTC_32c19.dir\\Debug\\cmTC_32c19.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_32c19.dir\\Debug\\cmTC_32c19.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_32c19.dir\\Debug\\cmTC_32c19.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX512 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_32c19.dir\\Debug\\\\" /Fd"cmTC_32c19.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4mz0dg\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX512 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_32c19.dir\\Debug\\\\" /Fd"cmTC_32c19.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4mz0dg\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4mz0dg\\Debug\\cmTC_32c19.exe" /INCREMENTAL /ILK:"cmTC_32c19.dir\\Debug\\cmTC_32c19.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4mz0dg/Debug/cmTC_32c19.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4mz0dg/Debug/cmTC_32c19.lib" /MACHINE:X64  /machine:x64 cmTC_32c19.dir\\Debug\\src.obj
          cmTC_32c19.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4mz0dg\\Debug\\cmTC_32c19.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_32c19.dir\\Debug\\cmTC_32c19.tlog\\unsuccessfulbuild".
          Touching "cmTC_32c19.dir\\Debug\\cmTC_32c19.tlog\\cmTC_32c19.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4mz0dg\\cmTC_32c19.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.53
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX512_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "external/portaudio/cmake/modules/FindRegex.cmake:23 (check_include_file)"
      - "external/portaudio/cmake/modules/FindJACK.cmake:38 (find_package)"
      - "external/portaudio/CMakeLists.txt:138 (find_package)"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-qp1eml"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-qp1eml"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: ";C:/AI/UltraFlexSTT_CPP/external/portaudio/cmake/modules"
    buildResult:
      variable: "REGEX_H"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-qp1eml'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_a9bbc.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/27/2025 3:52:30 PM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qp1eml\\cmTC_a9bbc.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_a9bbc.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qp1eml\\Debug\\".
          Creating directory "cmTC_a9bbc.dir\\Debug\\cmTC_a9bbc.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_a9bbc.dir\\Debug\\cmTC_a9bbc.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_a9bbc.dir\\Debug\\cmTC_a9bbc.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a9bbc.dir\\Debug\\\\" /Fd"cmTC_a9bbc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qp1eml\\CheckIncludeFile.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a9bbc.dir\\Debug\\\\" /Fd"cmTC_a9bbc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qp1eml\\CheckIncludeFile.c"
          CheckIncludeFile.c
        C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qp1eml\\CheckIncludeFile.c(1,10): error C1083: Cannot open include file: 'regex.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qp1eml\\cmTC_a9bbc.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qp1eml\\cmTC_a9bbc.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qp1eml\\cmTC_a9bbc.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qp1eml\\CheckIncludeFile.c(1,10): error C1083: Cannot open include file: 'regex.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qp1eml\\cmTC_a9bbc.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.37
        
      exitCode: 1
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - ARM64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/3.31.0-rc2/CompilerIdC/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/3.31.0-rc2/CompilerIdCXX/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pjs4gk"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pjs4gk"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pjs4gk'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_d97e8/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_d97e8.dir\\build.make CMakeFiles/cmTC_d97e8.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pjs4gk'
        Building C object CMakeFiles/cmTC_d97e8.dir/CMakeCCompilerABI.c.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe
        OFFLOAD_TARGET_NAMES=nvptx-none
        Target: x86_64-w64-mingw32
        Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_d97e8.dir\\'
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1.exe -quiet -v -iprefix C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_d97e8.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cczyZIKw.s
        GNU C23 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1) version 15.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.1.0, GMP version 6.3.0, MPFR version 4.2.2, MPC version 1.3.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"
        ignoring nonexistent directory "R:/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc/include"
        ignoring nonexistent directory "/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc/include"
        ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"
        ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: 20a66e05e8911d35cafb0ec9cc0fc57f
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_d97e8.dir\\'
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cczyZIKw.s
        GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (Binutils for MinGW-W64 x86_64, built by Brecht Sanders, r1) 2.44
        COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.'
        Linking C executable cmTC_d97e8.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_d97e8.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_d97e8.dir/objects.a
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_d97e8.dir/objects.a @CMakeFiles\\cmTC_d97e8.dir\\objects1.rsp
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_d97e8.dir/objects.a -Wl,--no-whole-archive -o cmTC_d97e8.exe -Wl,--out-implib,libcmTC_d97e8.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        OFFLOAD_TARGET_NAMES=nvptx-none
        Target: x86_64-w64-mingw32
        Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1) 
        COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d97e8.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_d97e8.'
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc7LZ47W.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_d97e8.exe C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_d97e8.dir/objects.a --no-whole-archive --out-implib libcmTC_d97e8.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        collect2 version 15.1.0
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc7LZ47W.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_d97e8.exe C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_d97e8.dir/objects.a --no-whole-archive --out-implib libcmTC_d97e8.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        GNU ld (Binutils for MinGW-W64 x86_64, built by Brecht Sanders, r1) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d97e8.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_d97e8.'
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pjs4gk'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/include]
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/include]
        implicit include dirs: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/include;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pjs4gk']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_d97e8/fast]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_d97e8.dir\\build.make CMakeFiles/cmTC_d97e8.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pjs4gk']
        ignore line: [Building C object CMakeFiles/cmTC_d97e8.dir/CMakeCCompilerABI.c.obj]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r1) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_d97e8.dir\\']
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1.exe -quiet -v -iprefix C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_d97e8.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cczyZIKw.s]
        ignore line: [GNU C23 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r1) version 15.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.1.0  GMP version 6.3.0  MPFR version 4.2.2  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"]
        ignore line: [ignoring nonexistent directory "R:/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc/include"]
        ignore line: [ignoring nonexistent directory "/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc/include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 20a66e05e8911d35cafb0ec9cc0fc57f]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_d97e8.dir\\']
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cczyZIKw.s]
        ignore line: [GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (Binutils for MinGW-W64 x86_64  built by Brecht Sanders  r1) 2.44]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_d97e8.dir\\CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_d97e8.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_d97e8.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_d97e8.dir/objects.a]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_d97e8.dir/objects.a @CMakeFiles\\cmTC_d97e8.dir\\objects1.rsp]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe  -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_d97e8.dir/objects.a -Wl --no-whole-archive -o cmTC_d97e8.exe -Wl --out-implib libcmTC_d97e8.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r1) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d97e8.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_d97e8.']
        link line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc7LZ47W.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_d97e8.exe C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_d97e8.dir/objects.a --no-whole-archive --out-implib libcmTC_d97e8.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc7LZ47W.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_d97e8.exe] ==> ignore
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_d97e8.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_d97e8.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        linker tool for 'C': Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
        collapse obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        implicit libs: [mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;mingwex;kernel32]
        implicit objs: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib/crt2.o;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        implicit dirs: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "--version"
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-otv4yx"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-otv4yx"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-otv4yx'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_8e053/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_8e053.dir\\build.make CMakeFiles/cmTC_8e053.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-otv4yx'
        Building CXX object CMakeFiles/cmTC_8e053.dir/CMakeCXXCompilerABI.cpp.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe   -v -o CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe
        OFFLOAD_TARGET_NAMES=nvptx-none
        Target: x86_64-w64-mingw32
        Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_8e053.dir\\'
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1plus.exe -quiet -v -iprefix C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_8e053.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccIuS5Cw.s
        GNU C++17 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1) version 15.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.1.0, GMP version 6.3.0, MPFR version 4.2.2, MPC version 1.3.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0"
        ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32"
        ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward"
        ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"
        ignoring nonexistent directory "R:/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc/include"
        ignoring nonexistent directory "/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc/include"
        ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"
        ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: 0c21c1a1bf15174c2cc5569bd91b4bfe
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_8e053.dir\\'
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccIuS5Cw.s
        GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (Binutils for MinGW-W64 x86_64, built by Brecht Sanders, r1) 2.44
        COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_8e053.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_8e053.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_8e053.dir/objects.a
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_8e053.dir/objects.a @CMakeFiles\\cmTC_8e053.dir\\objects1.rsp
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_8e053.dir/objects.a -Wl,--no-whole-archive -o cmTC_8e053.exe -Wl,--out-implib,libcmTC_8e053.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        OFFLOAD_TARGET_NAMES=nvptx-none
        Target: x86_64-w64-mingw32
        Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1) 
        COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8e053.exe' '-foffload-options=-l_GCC_stdc++' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_8e053.'
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc2sOMqL.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_8e053.exe C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_8e053.dir/objects.a --no-whole-archive --out-implib libcmTC_8e053.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        collect2 version 15.1.0
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc2sOMqL.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_8e053.exe C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_8e053.dir/objects.a --no-whole-archive --out-implib libcmTC_8e053.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        GNU ld (Binutils for MinGW-W64 x86_64, built by Brecht Sanders, r1) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8e053.exe' '-foffload-options=-l_GCC_stdc++' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_8e053.'
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-otv4yx'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0]
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32]
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward]
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
          add: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/include/c++/15.1.0]
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/include/c++/15.1.0/x86_64-w64-mingw32]
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/include/c++/15.1.0/backward]
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/include]
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        collapse include dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/include]
        implicit include dirs: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/include/c++/15.1.0;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/include/c++/15.1.0/x86_64-w64-mingw32;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/include/c++/15.1.0/backward;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/include;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-otv4yx']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_8e053/fast]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_8e053.dir\\build.make CMakeFiles/cmTC_8e053.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-otv4yx']
        ignore line: [Building CXX object CMakeFiles/cmTC_8e053.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe   -v -o CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r1) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_8e053.dir\\']
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1plus.exe -quiet -v -iprefix C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_8e053.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccIuS5Cw.s]
        ignore line: [GNU C++17 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r1) version 15.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.1.0  GMP version 6.3.0  MPFR version 4.2.2  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"]
        ignore line: [ignoring nonexistent directory "R:/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc/include"]
        ignore line: [ignoring nonexistent directory "/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc/include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 0c21c1a1bf15174c2cc5569bd91b4bfe]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_8e053.dir\\']
        ignore line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccIuS5Cw.s]
        ignore line: [GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (Binutils for MinGW-W64 x86_64  built by Brecht Sanders  r1) 2.44]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_8e053.dir\\CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_8e053.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_8e053.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_8e053.dir/objects.a]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_8e053.dir/objects.a @CMakeFiles\\cmTC_8e053.dir\\objects1.rsp]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe  -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_8e053.dir/objects.a -Wl --no-whole-archive -o cmTC_8e053.exe -Wl --out-implib libcmTC_8e053.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r1) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8e053.exe' '-foffload-options=-l_GCC_stdc++' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_8e053.']
        link line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc2sOMqL.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_8e053.exe C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_8e053.dir/objects.a --no-whole-archive --out-implib libcmTC_8e053.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc2sOMqL.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_8e053.exe] ==> ignore
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_8e053.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_8e053.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        linker tool for 'CXX': Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
        collapse obj [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc_s;gcc;mingwex;kernel32]
        implicit objs: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib/crt2.o;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        implicit dirs: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "--version"
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:16 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gepfe7"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gepfe7"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gepfe7'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_79eb9/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_79eb9.dir\\build.make CMakeFiles/cmTC_79eb9.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gepfe7'
        Building C object CMakeFiles/cmTC_79eb9.dir/src.c.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles\\cmTC_79eb9.dir\\src.c.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gepfe7\\src.c
        Linking C executable cmTC_79eb9.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_79eb9.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_79eb9.dir/objects.a
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_79eb9.dir/objects.a @CMakeFiles\\cmTC_79eb9.dir\\objects1.rsp
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe -Wl,--whole-archive CMakeFiles\\cmTC_79eb9.dir/objects.a -Wl,--no-whole-archive -o cmTC_79eb9.exe -Wl,--out-implib,libcmTC_79eb9.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\\cmTC_79eb9.dir\\linkLibs.rsp
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gepfe7'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-a1ypse"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-a1ypse"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_fopenmp"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-a1ypse'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_97c3c/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_97c3c.dir\\build.make CMakeFiles/cmTC_97c3c.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-a1ypse'
        Building C object CMakeFiles/cmTC_97c3c.dir/OpenMPTryFlag.c.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe   -fopenmp -std=gnu11 -o CMakeFiles\\cmTC_97c3c.dir\\OpenMPTryFlag.c.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a1ypse\\OpenMPTryFlag.c
        Linking C executable cmTC_97c3c.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_97c3c.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_97c3c.dir/objects.a
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_97c3c.dir/objects.a @CMakeFiles\\cmTC_97c3c.dir\\objects1.rsp
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe  -fopenmp -v -Wl,--whole-archive CMakeFiles\\cmTC_97c3c.dir/objects.a -Wl,--no-whole-archive -o cmTC_97c3c.exe -Wl,--out-implib,libcmTC_97c3c.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\\cmTC_97c3c.dir\\linkLibs.rsp
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        OFFLOAD_TARGET_NAMES=nvptx-none
        Target: x86_64-w64-mingw32
        Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1) 
        COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        Reading specs from C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/libgomp.spec
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'cmTC_97c3c.exe' '-mtune=generic' '-march=x86-64' '-mthreads' '-pthread' '-dumpdir' 'cmTC_97c3c.'
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccBLDh2d.res -plugin-opt=-pass-through=-lmingwthrd -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingwthrd -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_97c3c.exe C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. @C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccgy3jXh -lgomp -lmingwthrd -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingwthrd -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'cmTC_97c3c.exe' '-mtune=generic' '-march=x86-64' '-mthreads' '-pthread' '-dumpdir' 'cmTC_97c3c.'
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-a1ypse'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:296 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    message: |
      Parsed C OpenMP implicit link information from above output:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-a1ypse']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_97c3c/fast]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_97c3c.dir\\build.make CMakeFiles/cmTC_97c3c.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-a1ypse']
        ignore line: [Building C object CMakeFiles/cmTC_97c3c.dir/OpenMPTryFlag.c.obj]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe   -fopenmp -std=gnu11 -o CMakeFiles\\cmTC_97c3c.dir\\OpenMPTryFlag.c.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a1ypse\\OpenMPTryFlag.c]
        ignore line: [Linking C executable cmTC_97c3c.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_97c3c.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_97c3c.dir/objects.a]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_97c3c.dir/objects.a @CMakeFiles\\cmTC_97c3c.dir\\objects1.rsp]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe  -fopenmp -v -Wl --whole-archive CMakeFiles\\cmTC_97c3c.dir/objects.a -Wl --no-whole-archive -o cmTC_97c3c.exe -Wl --out-implib libcmTC_97c3c.dll.a -Wl --major-image-version 0 --minor-image-version 0 @CMakeFiles\\cmTC_97c3c.dir\\linkLibs.rsp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r1) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [Reading specs from C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/libgomp.spec]
        ignore line: [COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'cmTC_97c3c.exe' '-mtune=generic' '-march=x86-64' '-mthreads' '-pthread' '-dumpdir' 'cmTC_97c3c.']
        link line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccBLDh2d.res -plugin-opt=-pass-through=-lmingwthrd -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingwthrd -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_97c3c.exe C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. @C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccgy3jXh -lgomp -lmingwthrd -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingwthrd -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccBLDh2d.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwthrd] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwthrd] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_97c3c.exe] ==> ignore
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [@C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccgy3jXh] ==> ignore
          arg [-lgomp] ==> lib [gomp]
          arg [-lmingwthrd] ==> lib [mingwthrd]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingwthrd] ==> lib [mingwthrd]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        implicit libs: [gomp;mingwthrd;mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingwthrd;mingw32;gcc;mingwex;kernel32]
        implicit objs: []
        implicit dirs: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gj60je"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gj60je"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_fopenmp"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gj60je'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_f1a73/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_f1a73.dir\\build.make CMakeFiles/cmTC_f1a73.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gj60je'
        Building CXX object CMakeFiles/cmTC_f1a73.dir/OpenMPTryFlag.cpp.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe   -fopenmp -std=gnu++17 -o CMakeFiles\\cmTC_f1a73.dir\\OpenMPTryFlag.cpp.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gj60je\\OpenMPTryFlag.cpp
        Linking CXX executable cmTC_f1a73.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_f1a73.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_f1a73.dir/objects.a
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_f1a73.dir/objects.a @CMakeFiles\\cmTC_f1a73.dir\\objects1.rsp
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe  -fopenmp -v -Wl,--whole-archive CMakeFiles\\cmTC_f1a73.dir/objects.a -Wl,--no-whole-archive -o cmTC_f1a73.exe -Wl,--out-implib,libcmTC_f1a73.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\\cmTC_f1a73.dir\\linkLibs.rsp
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        OFFLOAD_TARGET_NAMES=nvptx-none
        Target: x86_64-w64-mingw32
        Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1) 
        COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        Reading specs from C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/libgomp.spec
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'cmTC_f1a73.exe' '-foffload-options=-l_GCC_stdc++' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-mthreads' '-pthread' '-dumpdir' 'cmTC_f1a73.'
         C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8jpq8h.res -plugin-opt=-pass-through=-lmingwthrd -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingwthrd -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_f1a73.exe C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. @C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKMDw9p -lgomp -lmingwthrd -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingwthrd -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'cmTC_f1a73.exe' '-foffload-options=-l_GCC_stdc++' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-mthreads' '-pthread' '-dumpdir' 'cmTC_f1a73.'
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gj60je'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:296 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    message: |
      Parsed CXX OpenMP implicit link information from above output:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gj60je']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_f1a73/fast]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_f1a73.dir\\build.make CMakeFiles/cmTC_f1a73.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-gj60je']
        ignore line: [Building CXX object CMakeFiles/cmTC_f1a73.dir/OpenMPTryFlag.cpp.obj]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe   -fopenmp -std=gnu++17 -o CMakeFiles\\cmTC_f1a73.dir\\OpenMPTryFlag.cpp.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gj60je\\OpenMPTryFlag.cpp]
        ignore line: [Linking CXX executable cmTC_f1a73.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_f1a73.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_f1a73.dir/objects.a]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_f1a73.dir/objects.a @CMakeFiles\\cmTC_f1a73.dir\\objects1.rsp]
        ignore line: [C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe  -fopenmp -v -Wl --whole-archive CMakeFiles\\cmTC_f1a73.dir/objects.a -Wl --no-whole-archive -o cmTC_f1a73.exe -Wl --out-implib libcmTC_f1a73.dll.a -Wl --major-image-version 0 --minor-image-version 0 @CMakeFiles\\cmTC_f1a73.dir\\linkLibs.rsp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-15.1.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r1' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --enable-libgdiagnostics --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-15.1.0/build_mingw/mingw-w64 CFLAGS='-I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.1.0 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r1) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [Reading specs from C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/libgomp.spec]
        ignore line: [COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'cmTC_f1a73.exe' '-foffload-options=-l_GCC_stdc++' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-mthreads' '-pthread' '-dumpdir' 'cmTC_f1a73.']
        link line: [ C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8jpq8h.res -plugin-opt=-pass-through=-lmingwthrd -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingwthrd -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_f1a73.exe C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. @C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKMDw9p -lgomp -lmingwthrd -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingwthrd -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8jpq8h.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwthrd] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwthrd] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_f1a73.exe] ==> ignore
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [@C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKMDw9p] ==> ignore
          arg [-lgomp] ==> lib [gomp]
          arg [-lmingwthrd] ==> lib [mingwthrd]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingwthrd] ==> lib [mingwthrd]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        implicit libs: [gomp;mingwthrd;mingw32;gcc_s;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingwthrd;mingw32;gcc_s;gcc;mingwex;kernel32]
        implicit objs: []
        implicit dirs: [C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib/gcc;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/x86_64-w64-mingw32/lib;C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:456 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:596 (_OPENMP_GET_SPEC_DATE)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP version"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0h45so"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0h45so"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_C_"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0h45so'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_8ffc3/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_8ffc3.dir\\build.make CMakeFiles/cmTC_8ffc3.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0h45so'
        Building C object CMakeFiles/cmTC_8ffc3.dir/OpenMPCheckVersion.c.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe   -fopenmp -std=gnu11 -o CMakeFiles\\cmTC_8ffc3.dir\\OpenMPCheckVersion.c.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0h45so\\OpenMPCheckVersion.c
        Linking C executable cmTC_8ffc3.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_8ffc3.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_8ffc3.dir/objects.a
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_8ffc3.dir/objects.a @CMakeFiles\\cmTC_8ffc3.dir\\objects1.rsp
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe  -fopenmp -Wl,--whole-archive CMakeFiles\\cmTC_8ffc3.dir/objects.a -Wl,--no-whole-archive -o cmTC_8ffc3.exe -Wl,--out-implib,libcmTC_8ffc3.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\\cmTC_8ffc3.dir\\linkLibs.rsp
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0h45so'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:456 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:596 (_OPENMP_GET_SPEC_DATE)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP version"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-b17uv9"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-b17uv9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_CXX_"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-b17uv9'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_045f9/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_045f9.dir\\build.make CMakeFiles/cmTC_045f9.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-b17uv9'
        Building CXX object CMakeFiles/cmTC_045f9.dir/OpenMPCheckVersion.cpp.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe   -fopenmp -std=gnu++17 -o CMakeFiles\\cmTC_045f9.dir\\OpenMPCheckVersion.cpp.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-b17uv9\\OpenMPCheckVersion.cpp
        Linking CXX executable cmTC_045f9.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_045f9.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_045f9.dir/objects.a
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_045f9.dir/objects.a @CMakeFiles\\cmTC_045f9.dir\\objects1.rsp
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe  -fopenmp -Wl,--whole-archive CMakeFiles\\cmTC_045f9.dir/objects.a -Wl,--no-whole-archive -o cmTC_045f9.exe -Wl,--out-implib,libcmTC_045f9.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\\cmTC_045f9.dir\\linkLibs.rsp
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-b17uv9'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:95 (check_cxx_compiler_flag)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test GGML_COMPILER_SUPPORTS_FP16_FORMAT_I3E"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-phnrrl"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-phnrrl"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "GGML_COMPILER_SUPPORTS_FP16_FORMAT_I3E"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-phnrrl'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_fc02d/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_fc02d.dir\\build.make CMakeFiles/cmTC_fc02d.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-phnrrl'
        Building CXX object CMakeFiles/cmTC_fc02d.dir/src.cxx.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe -DGGML_COMPILER_SUPPORTS_FP16_FORMAT_I3E  -std=gnu++17   -mfp16-format=ieee -o CMakeFiles\\cmTC_fc02d.dir\\src.cxx.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-phnrrl\\src.cxx
        g++.exe: error: unrecognized command-line option '-mfp16-format=ieee'
        mingw32-make[1]: *** [CMakeFiles\\cmTC_fc02d.dir\\build.make:80: CMakeFiles/cmTC_fc02d.dir/src.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-phnrrl'
        mingw32-make: *** [Makefile:132: cmTC_fc02d/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:124 (check_cxx_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:137 (check_arm_feature)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test GGML_MACHINE_SUPPORTS_dotprod"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-difz04"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-difz04"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "GGML_MACHINE_SUPPORTS_dotprod_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-difz04'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_f7eea/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_f7eea.dir\\build.make CMakeFiles/cmTC_f7eea.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-difz04'
        Building CXX object CMakeFiles/cmTC_f7eea.dir/src.cxx.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe -DGGML_MACHINE_SUPPORTS_dotprod  -mcpu=native+dotprod -std=gnu++17 -o CMakeFiles\\cmTC_f7eea.dir\\src.cxx.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-difz04\\src.cxx
        g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
        cc1plus.exe: error: bad value 'native+dotprod' for '-mtune=' switch
        cc1plus.exe: note: valid arguments to '-mtune=' switch are: nocona core2 nehalem corei7 westmere sandybridge corei7-avx ivybridge core-avx-i haswell core-avx2 broadwell skylake skylake-avx512 cannonlake icelake-client rocketlake icelake-server cascadelake tigerlake cooperlake sapphirerapids emeraldrapids alderlake raptorlake meteorlake graniterapids graniterapids-d arrowlake arrowlake-s lunarlake pantherlake diamondrapids bonnell atom silvermont slm goldmont goldmont-plus tremont gracemont sierraforest grandridge clearwaterforest intel x86-64 eden-x2 nano nano-1000 nano-2000 nano-3000 nano-x2 eden-x4 nano-x4 lujiazui yongfeng shijidadao k8 k8-sse3 opteron opteron-sse3 athlon64 athlon64-sse3 athlon-fx amdfam10 barcelona bdver1 bdver2 bdver3 bdver4 znver1 znver2 znver3 znver4 znver5 btver1 btver2 generic native
        mingw32-make[1]: *** [CMakeFiles\\cmTC_f7eea.dir\\build.make:80: CMakeFiles/cmTC_f7eea.dir/src.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-difz04'
        mingw32-make: *** [Makefile:132: cmTC_f7eea/fast] Error 2
        
      exitCode: 2
    runResult:
      variable: "GGML_MACHINE_SUPPORTS_dotprod_EXITCODE"
      cached: true
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:129 (check_cxx_source_compiles)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:137 (check_arm_feature)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test GGML_MACHINE_SUPPORTS_nodotprod"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-hp7e4z"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-hp7e4z"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "GGML_MACHINE_SUPPORTS_nodotprod"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-hp7e4z'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_7a1e9/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_7a1e9.dir\\build.make CMakeFiles/cmTC_7a1e9.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-hp7e4z'
        Building CXX object CMakeFiles/cmTC_7a1e9.dir/src.cxx.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe -DGGML_MACHINE_SUPPORTS_nodotprod  -mcpu=native+nodotprod -std=gnu++17 -o CMakeFiles\\cmTC_7a1e9.dir\\src.cxx.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hp7e4z\\src.cxx
        g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
        cc1plus.exe: error: bad value 'native+nodotprod' for '-mtune=' switch
        cc1plus.exe: note: valid arguments to '-mtune=' switch are: nocona core2 nehalem corei7 westmere sandybridge corei7-avx ivybridge core-avx-i haswell core-avx2 broadwell skylake skylake-avx512 cannonlake icelake-client rocketlake icelake-server cascadelake tigerlake cooperlake sapphirerapids emeraldrapids alderlake raptorlake meteorlake graniterapids graniterapids-d arrowlake arrowlake-s lunarlake pantherlake diamondrapids bonnell atom silvermont slm goldmont goldmont-plus tremont gracemont sierraforest grandridge clearwaterforest intel x86-64 eden-x2 nano nano-1000 nano-2000 nano-3000 nano-x2 eden-x4 nano-x4 lujiazui yongfeng shijidadao k8 k8-sse3 opteron opteron-sse3 athlon64 athlon64-sse3 athlon-fx amdfam10 barcelona bdver1 bdver2 bdver3 bdver4 znver1 znver2 znver3 znver4 znver5 btver1 btver2 generic native
        mingw32-make[1]: *** [CMakeFiles\\cmTC_7a1e9.dir\\build.make:80: CMakeFiles/cmTC_7a1e9.dir/src.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-hp7e4z'
        mingw32-make: *** [Makefile:132: cmTC_7a1e9/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:124 (check_cxx_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:138 (check_arm_feature)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test GGML_MACHINE_SUPPORTS_i8mm"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-bgb7vx"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-bgb7vx"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "GGML_MACHINE_SUPPORTS_i8mm_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-bgb7vx'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_6b641/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_6b641.dir\\build.make CMakeFiles/cmTC_6b641.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-bgb7vx'
        Building CXX object CMakeFiles/cmTC_6b641.dir/src.cxx.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe -DGGML_MACHINE_SUPPORTS_i8mm  -mcpu=native+i8mm -std=gnu++17 -o CMakeFiles\\cmTC_6b641.dir\\src.cxx.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bgb7vx\\src.cxx
        g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
        cc1plus.exe: error: bad value 'native+i8mm' for '-mtune=' switch
        cc1plus.exe: note: valid arguments to '-mtune=' switch are: nocona core2 nehalem corei7 westmere sandybridge corei7-avx ivybridge core-avx-i haswell core-avx2 broadwell skylake skylake-avx512 cannonlake icelake-client rocketlake icelake-server cascadelake tigerlake cooperlake sapphirerapids emeraldrapids alderlake raptorlake meteorlake graniterapids graniterapids-d arrowlake arrowlake-s lunarlake pantherlake diamondrapids bonnell atom silvermont slm goldmont goldmont-plus tremont gracemont sierraforest grandridge clearwaterforest intel x86-64 eden-x2 nano nano-1000 nano-2000 nano-3000 nano-x2 eden-x4 nano-x4 lujiazui yongfeng shijidadao k8 k8-sse3 opteron opteron-sse3 athlon64 athlon64-sse3 athlon-fx amdfam10 barcelona bdver1 bdver2 bdver3 bdver4 znver1 znver2 znver3 znver4 znver5 btver1 btver2 generic native
        mingw32-make[1]: *** [CMakeFiles\\cmTC_6b641.dir\\build.make:80: CMakeFiles/cmTC_6b641.dir/src.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-bgb7vx'
        mingw32-make: *** [Makefile:132: cmTC_6b641/fast] Error 2
        
      exitCode: 2
    runResult:
      variable: "GGML_MACHINE_SUPPORTS_i8mm_EXITCODE"
      cached: true
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:129 (check_cxx_source_compiles)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:138 (check_arm_feature)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test GGML_MACHINE_SUPPORTS_noi8mm"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-oi31bs"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-oi31bs"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "GGML_MACHINE_SUPPORTS_noi8mm"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-oi31bs'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_0e84c/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_0e84c.dir\\build.make CMakeFiles/cmTC_0e84c.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-oi31bs'
        Building CXX object CMakeFiles/cmTC_0e84c.dir/src.cxx.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe -DGGML_MACHINE_SUPPORTS_noi8mm  -mcpu=native+noi8mm -std=gnu++17 -o CMakeFiles\\cmTC_0e84c.dir\\src.cxx.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oi31bs\\src.cxx
        g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
        cc1plus.exe: error: bad value 'native+noi8mm' for '-mtune=' switch
        cc1plus.exe: note: valid arguments to '-mtune=' switch are: nocona core2 nehalem corei7 westmere sandybridge corei7-avx ivybridge core-avx-i haswell core-avx2 broadwell skylake skylake-avx512 cannonlake icelake-client rocketlake icelake-server cascadelake tigerlake cooperlake sapphirerapids emeraldrapids alderlake raptorlake meteorlake graniterapids graniterapids-d arrowlake arrowlake-s lunarlake pantherlake diamondrapids bonnell atom silvermont slm goldmont goldmont-plus tremont gracemont sierraforest grandridge clearwaterforest intel x86-64 eden-x2 nano nano-1000 nano-2000 nano-3000 nano-x2 eden-x4 nano-x4 lujiazui yongfeng shijidadao k8 k8-sse3 opteron opteron-sse3 athlon64 athlon64-sse3 athlon-fx amdfam10 barcelona bdver1 bdver2 bdver3 bdver4 znver1 znver2 znver3 znver4 znver5 btver1 btver2 generic native
        mingw32-make[1]: *** [CMakeFiles\\cmTC_0e84c.dir\\build.make:80: CMakeFiles/cmTC_0e84c.dir/src.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-oi31bs'
        mingw32-make: *** [Makefile:132: cmTC_0e84c/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:124 (check_cxx_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:139 (check_arm_feature)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test GGML_MACHINE_SUPPORTS_sve"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0avc5f"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0avc5f"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "GGML_MACHINE_SUPPORTS_sve_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0avc5f'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_be92f/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_be92f.dir\\build.make CMakeFiles/cmTC_be92f.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0avc5f'
        Building CXX object CMakeFiles/cmTC_be92f.dir/src.cxx.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe -DGGML_MACHINE_SUPPORTS_sve  -mcpu=native+sve -std=gnu++17 -o CMakeFiles\\cmTC_be92f.dir\\src.cxx.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0avc5f\\src.cxx
        g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
        cc1plus.exe: error: bad value 'native+sve' for '-mtune=' switch
        cc1plus.exe: note: valid arguments to '-mtune=' switch are: nocona core2 nehalem corei7 westmere sandybridge corei7-avx ivybridge core-avx-i haswell core-avx2 broadwell skylake skylake-avx512 cannonlake icelake-client rocketlake icelake-server cascadelake tigerlake cooperlake sapphirerapids emeraldrapids alderlake raptorlake meteorlake graniterapids graniterapids-d arrowlake arrowlake-s lunarlake pantherlake diamondrapids bonnell atom silvermont slm goldmont goldmont-plus tremont gracemont sierraforest grandridge clearwaterforest intel x86-64 eden-x2 nano nano-1000 nano-2000 nano-3000 nano-x2 eden-x4 nano-x4 lujiazui yongfeng shijidadao k8 k8-sse3 opteron opteron-sse3 athlon64 athlon64-sse3 athlon-fx amdfam10 barcelona bdver1 bdver2 bdver3 bdver4 znver1 znver2 znver3 znver4 znver5 btver1 btver2 generic native; did you mean 'native'?
        mingw32-make[1]: *** [CMakeFiles\\cmTC_be92f.dir\\build.make:80: CMakeFiles/cmTC_be92f.dir/src.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0avc5f'
        mingw32-make: *** [Makefile:132: cmTC_be92f/fast] Error 2
        
      exitCode: 2
    runResult:
      variable: "GGML_MACHINE_SUPPORTS_sve_EXITCODE"
      cached: true
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:129 (check_cxx_source_compiles)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:139 (check_arm_feature)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test GGML_MACHINE_SUPPORTS_nosve"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-04thnm"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-04thnm"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "GGML_MACHINE_SUPPORTS_nosve"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-04thnm'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_ab800/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_ab800.dir\\build.make CMakeFiles/cmTC_ab800.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-04thnm'
        Building CXX object CMakeFiles/cmTC_ab800.dir/src.cxx.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe -DGGML_MACHINE_SUPPORTS_nosve  -mcpu=native+nosve -std=gnu++17 -o CMakeFiles\\cmTC_ab800.dir\\src.cxx.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-04thnm\\src.cxx
        g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
        cc1plus.exe: error: bad value 'native+nosve' for '-mtune=' switch
        cc1plus.exe: note: valid arguments to '-mtune=' switch are: nocona core2 nehalem corei7 westmere sandybridge corei7-avx ivybridge core-avx-i haswell core-avx2 broadwell skylake skylake-avx512 cannonlake icelake-client rocketlake icelake-server cascadelake tigerlake cooperlake sapphirerapids emeraldrapids alderlake raptorlake meteorlake graniterapids graniterapids-d arrowlake arrowlake-s lunarlake pantherlake diamondrapids bonnell atom silvermont slm goldmont goldmont-plus tremont gracemont sierraforest grandridge clearwaterforest intel x86-64 eden-x2 nano nano-1000 nano-2000 nano-3000 nano-x2 eden-x4 nano-x4 lujiazui yongfeng shijidadao k8 k8-sse3 opteron opteron-sse3 athlon64 athlon64-sse3 athlon-fx amdfam10 barcelona bdver1 bdver2 bdver3 bdver4 znver1 znver2 znver3 znver4 znver5 btver1 btver2 generic native
        mingw32-make[1]: *** [CMakeFiles\\cmTC_ab800.dir\\build.make:80: CMakeFiles/cmTC_ab800.dir/src.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-04thnm'
        mingw32-make: *** [Makefile:132: cmTC_ab800/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:124 (check_cxx_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:140 (check_arm_feature)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test GGML_MACHINE_SUPPORTS_sme"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-cp2ezx"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-cp2ezx"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "GGML_MACHINE_SUPPORTS_sme_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-cp2ezx'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_9e155/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_9e155.dir\\build.make CMakeFiles/cmTC_9e155.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-cp2ezx'
        Building CXX object CMakeFiles/cmTC_9e155.dir/src.cxx.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe -DGGML_MACHINE_SUPPORTS_sme  -mcpu=native+sme -std=gnu++17 -o CMakeFiles\\cmTC_9e155.dir\\src.cxx.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cp2ezx\\src.cxx
        g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
        cc1plus.exe: error: bad value 'native+sme' for '-mtune=' switch
        cc1plus.exe: note: valid arguments to '-mtune=' switch are: nocona core2 nehalem corei7 westmere sandybridge corei7-avx ivybridge core-avx-i haswell core-avx2 broadwell skylake skylake-avx512 cannonlake icelake-client rocketlake icelake-server cascadelake tigerlake cooperlake sapphirerapids emeraldrapids alderlake raptorlake meteorlake graniterapids graniterapids-d arrowlake arrowlake-s lunarlake pantherlake diamondrapids bonnell atom silvermont slm goldmont goldmont-plus tremont gracemont sierraforest grandridge clearwaterforest intel x86-64 eden-x2 nano nano-1000 nano-2000 nano-3000 nano-x2 eden-x4 nano-x4 lujiazui yongfeng shijidadao k8 k8-sse3 opteron opteron-sse3 athlon64 athlon64-sse3 athlon-fx amdfam10 barcelona bdver1 bdver2 bdver3 bdver4 znver1 znver2 znver3 znver4 znver5 btver1 btver2 generic native; did you mean 'native'?
        mingw32-make[1]: *** [CMakeFiles\\cmTC_9e155.dir\\build.make:80: CMakeFiles/cmTC_9e155.dir/src.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-cp2ezx'
        mingw32-make: *** [Makefile:132: cmTC_9e155/fast] Error 2
        
      exitCode: 2
    runResult:
      variable: "GGML_MACHINE_SUPPORTS_sme_EXITCODE"
      cached: true
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:129 (check_cxx_source_compiles)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:140 (check_arm_feature)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test GGML_MACHINE_SUPPORTS_nosme"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vo0gdf"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vo0gdf"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "GGML_MACHINE_SUPPORTS_nosme"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vo0gdf'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_d3ed4/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_d3ed4.dir\\build.make CMakeFiles/cmTC_d3ed4.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vo0gdf'
        Building CXX object CMakeFiles/cmTC_d3ed4.dir/src.cxx.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\g++.exe -DGGML_MACHINE_SUPPORTS_nosme  -mcpu=native+nosme -std=gnu++17 -o CMakeFiles\\cmTC_d3ed4.dir\\src.cxx.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vo0gdf\\src.cxx
        g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
        cc1plus.exe: error: bad value 'native+nosme' for '-mtune=' switch
        cc1plus.exe: note: valid arguments to '-mtune=' switch are: nocona core2 nehalem corei7 westmere sandybridge corei7-avx ivybridge core-avx-i haswell core-avx2 broadwell skylake skylake-avx512 cannonlake icelake-client rocketlake icelake-server cascadelake tigerlake cooperlake sapphirerapids emeraldrapids alderlake raptorlake meteorlake graniterapids graniterapids-d arrowlake arrowlake-s lunarlake pantherlake diamondrapids bonnell atom silvermont slm goldmont goldmont-plus tremont gracemont sierraforest grandridge clearwaterforest intel x86-64 eden-x2 nano nano-1000 nano-2000 nano-3000 nano-x2 eden-x4 nano-x4 lujiazui yongfeng shijidadao k8 k8-sse3 opteron opteron-sse3 athlon64 athlon64-sse3 athlon-fx amdfam10 barcelona bdver1 bdver2 bdver3 bdver4 znver1 znver2 znver3 znver4 znver5 btver1 btver2 generic native
        mingw32-make[1]: *** [CMakeFiles\\cmTC_d3ed4.dir\\build.make:80: CMakeFiles/cmTC_d3ed4.dir/src.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vo0gdf'
        mingw32-make: *** [Makefile:132: cmTC_d3ed4/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "external/portaudio/cmake/modules/FindRegex.cmake:23 (check_include_file)"
      - "external/portaudio/cmake/modules/FindJACK.cmake:38 (find_package)"
      - "external/portaudio/CMakeLists.txt:138 (find_package)"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-obetgl"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-obetgl"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: ";C:/AI/UltraFlexSTT_CPP/external/portaudio/cmake/modules"
    buildResult:
      variable: "REGEX_H"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-obetgl'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe -f Makefile cmTC_9d1cb/fast
        C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_9d1cb.dir\\build.make CMakeFiles/cmTC_9d1cb.dir/build
        mingw32-make[1]: Entering directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-obetgl'
        Building C object CMakeFiles/cmTC_9d1cb.dir/CheckIncludeFile.c.obj
        C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe\\mingw64\\bin\\gcc.exe    -o CMakeFiles\\cmTC_9d1cb.dir\\CheckIncludeFile.c.obj -c C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-obetgl\\CheckIncludeFile.c
        C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-obetgl\\CheckIncludeFile.c:1:10: fatal error: regex.h: No such file or directory
            1 | #include <regex.h>
              |          ^~~~~~~~~
        compilation terminated.
        mingw32-make[1]: *** [CMakeFiles\\cmTC_9d1cb.dir\\build.make:80: CMakeFiles/cmTC_9d1cb.dir/CheckIncludeFile.c.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-obetgl'
        mingw32-make: *** [Makefile:132: cmTC_9d1cb/fast] Error 2
        
      exitCode: 2
...
